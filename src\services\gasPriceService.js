const axios = require('axios');
const { supabase } = require('../config/database');

// API 설정 상수
const JEJU_API_URL = 'http://api.jejuits.go.kr/api/infoGasPriceList';
const JEJU_API_CODE = '860665';

class GasPriceService {
  constructor() {
    this.apiUrl = JEJU_API_URL;
    this.apiCode = JEJU_API_CODE;
  }

  /**
   * 제주도 주유소 가격 API에서 데이터를 가져옵니다.
   */
  async fetchGasPriceData() {
    try {
      console.log('제주도 주유소 가격 API 호출 중...');
      
      const response = await axios.get(this.apiUrl, {
        params: {
          code: this.apiCode
        },
        timeout: 30000 // 30초 타임아웃
      });

      if (response.data.result !== 'success') {
        throw new Error(`API 호출 실패: ${response.data.result}`);
      }

      console.log(`API 응답 성공: ${response.data.info_cnt}개의 주유소 정보 수신`);
      return response.data.info;
    } catch (error) {
      console.error('API 호출 중 오류 발생:', error.message);
      throw error;
    }
  }

  /**
   * API 데이터를 DB 스키마에 맞게 변환합니다.
   */
  transformApiDataToDbFormat(apiData) {
    const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD 형식
    
    return apiData.map(item => ({
      opinet_id: item.id,
      gasoline_price: item.gasoline || 0,
      premium_gasoline_price: item.premium_gasoline || 0,
      diesel_price: item.diesel || 0,
      lpg_price: item.lpg || 0,
      price_date: currentDate,
      api_raw_data: JSON.stringify(item),
      fetched_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));
  }

  /**
   * 단일 주유소 데이터를 upsert합니다.
   */
  async upsertGasPrice(gasStationData) {
    try {
      const { data, error } = await supabase
        .from('gas_prices')
        .upsert(gasStationData, {
          onConflict: 'opinet_id,price_date'
        });

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error(`주유소 ${gasStationData.opinet_id} 데이터 저장 실패:`, error.message);
      throw error;
    }
  }

  /**
   * 여러 주유소 데이터를 배치로 upsert합니다.
   */
  async batchUpsertGasPrices(gasStationsData) {
    try {
      console.log(`${gasStationsData.length}개의 주유소 데이터 저장 중...`);
      
      const { data, error } = await supabase
        .from('gas_prices')
        .upsert(gasStationsData, {
          onConflict: 'opinet_id,price_date'
        });

      if (error) {
        throw error;
      }

      console.log(`${gasStationsData.length}개의 주유소 데이터 저장 완료`);
      return data;
    } catch (error) {
      console.error('배치 데이터 저장 실패:', error.message);
      throw error;
    }
  }

  /**
   * 전체 프로세스를 실행합니다: API 호출 → 데이터 변환 → DB 저장
   */
  async syncGasPrices() {
    try {
      console.log('=== 주유소 가격 동기화 시작 ===');
      const startTime = new Date();

      // 1. API에서 데이터 가져오기
      const apiData = await this.fetchGasPriceData();
      
      if (!apiData || apiData.length === 0) {
        console.log('API에서 받은 데이터가 없습니다.');
        return;
      }

      // 2. 데이터 변환
      const dbData = this.transformApiDataToDbFormat(apiData);

      // 3. DB에 저장
      await this.batchUpsertGasPrices(dbData);

      const endTime = new Date();
      const duration = (endTime - startTime) / 1000;
      
      console.log(`=== 주유소 가격 동기화 완료 (소요시간: ${duration}초) ===`);
      
      return {
        success: true,
        processedCount: dbData.length,
        duration: duration
      };
    } catch (error) {
      console.error('주유소 가격 동기화 실패:', error.message);
      throw error;
    }
  }
}

module.exports = GasPriceService;
