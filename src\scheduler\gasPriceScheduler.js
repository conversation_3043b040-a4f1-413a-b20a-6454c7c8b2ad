const cron = require('node-cron');
const GasPriceService = require('../services/gasPriceService');

// 스케줄러 설정 상수
const SCHEDULE_CRON = '5 2,8,14,20 * * *'; // 새벽 2시 5분 + 6시간마다 (8시, 14시, 20시)

class GasPriceScheduler {
  constructor() {
    this.gasPriceService = new GasPriceService();
    this.scheduleCron = SCHEDULE_CRON;
    this.isRunning = false;
  }

  /**
   * 스케줄러를 시작합니다.
   */
  start() {
    console.log(`스케줄러 시작 - 실행 주기: ${this.scheduleCron}`);
    console.log('다음 실행 시간들:');
    
    // 다음 5번의 실행 시간을 미리 보여줍니다
    const task = cron.schedule(this.scheduleCron, () => {}, { scheduled: false });
    
    cron.schedule(this.scheduleCron, async () => {
      if (this.isRunning) {
        console.log('이전 작업이 아직 실행 중입니다. 이번 실행을 건너뜁니다.');
        return;
      }

      this.isRunning = true;
      
      try {
        console.log(`\n[${new Date().toISOString()}] 스케줄된 주유소 가격 동기화 시작`);
        await this.gasPriceService.syncGasPrices();
        console.log(`[${new Date().toISOString()}] 스케줄된 주유소 가격 동기화 완료\n`);
      } catch (error) {
        console.error(`[${new Date().toISOString()}] 스케줄된 작업 실패:`, error.message);
      } finally {
        this.isRunning = false;
      }
    });

    console.log('스케줄러가 성공적으로 시작되었습니다.');
  }

  /**
   * 즉시 한 번 실행합니다 (테스트용).
   */
  async runOnce() {
    if (this.isRunning) {
      console.log('작업이 이미 실행 중입니다.');
      return;
    }

    this.isRunning = true;
    
    try {
      console.log(`\n[${new Date().toISOString()}] 수동 주유소 가격 동기화 시작`);
      const result = await this.gasPriceService.syncGasPrices();
      console.log(`[${new Date().toISOString()}] 수동 주유소 가격 동기화 완료\n`);
      return result;
    } catch (error) {
      console.error(`[${new Date().toISOString()}] 수동 작업 실패:`, error.message);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 현재 실행 상태를 반환합니다.
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      scheduleCron: this.scheduleCron,
      nextRun: this.getNextRunTime()
    };
  }

  /**
   * 다음 실행 시간을 계산합니다.
   */
  getNextRunTime() {
    // 간단한 다음 실행 시간 계산 (정확하지 않을 수 있음)
    const now = new Date();
    const cronParts = this.scheduleCron.split(' ');
    
    if (cronParts.length >= 5) {
      const minutes = cronParts[0];
      const hours = cronParts[1];
      
      if (hours.includes('*/')) {
        const interval = parseInt(hours.split('*/')[1]);
        const nextHour = Math.ceil(now.getHours() / interval) * interval;
        const nextRun = new Date(now);
        nextRun.setHours(nextHour, parseInt(minutes) || 0, 0, 0);
        
        if (nextRun <= now) {
          nextRun.setDate(nextRun.getDate() + 1);
        }
        
        return nextRun.toISOString();
      }
    }
    
    return '계산할 수 없음';
  }
}

module.exports = GasPriceScheduler;
